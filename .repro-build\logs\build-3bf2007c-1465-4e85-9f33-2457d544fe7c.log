=== Repro-Build Log ===
Build ID: 3bf2007c-1465-4e85-9f33-2457d544fe7c
Timestamp: 1748459199
=====================

[1748459199] Build Configuration:
  Docker Image: nixos/nix:latest
  Targets: x86_64-w64-mingw32
  Build ID: 3bf2007c-1465-4e85-9f33-2457d544fe7c
  Project Path: \\?\C:\Users\<USER>\dev\repro-build
  Rust Channel: stable
  Rust Version: latest

[1748459199] Generating flake.nix file
[1748459199] Generated flake.nix in \\?\C:\Users\<USER>\dev\repro-build\.repro-build\flake.nix
[1748459199] Setting up Docker container
[1748459200] Created container with ID: 2d63016037b272ca9855192fd0d32b01c629435888b1cdb9e933649ee7ed3a69
[1748459200] Configuring git safe directory in container
[1748459200] Command: git config --global --add safe.directory /app
Output:

--------------------------------------------------------------------------------

[1748459200] Using existing flake.lock
[1748459200] Starting build for targets: x86_64-w64-mingw32
[1748459200] Command: mkdir -p ./target/repro-build
Output:

--------------------------------------------------------------------------------

[1748459200] Starting build process for 1 target(s)...
[1748459200] Building for target: x86_64-w64-mingw32
