[package]
name = "repro_build"
version = "0.1.0"
edition = "2021"
description = "Tool for reproducible builds using Nix inside Docker containers"
license = "MIT"
repository = "https://github.com/your-username/repro-build"
readme = "README.md"
keywords = ["nix", "docker", "build", "reproducible", "cross-compilation"]
categories = ["development-tools::build-utils", "command-line-utilities"]
include = [
    "src/**/*",
    "templates/**/*",
    "Cargo.toml",
    "README.md"
]

[dependencies]
anyhow = "1.0.98"
bollard = "0.18.1"
cargo_metadata = "0.19.2"
clap = { version = "4.5.38", features = ["derive"] }
futures-util = "0.3.31"
serde = { version = "1.0.219", features = ["derive"] }
tera = "1.20.0"
tokio = { version = "1.45.0", features = ["full"] }
uuid = { version = "1.7.0", features = ["v4"] }

# make release builds faster and smaller
[profile.release]
lto = true
opt-level = "s"
codegen-units = 1
panic = "abort"
strip = true

[lib]
name = "repro_build_lib"
path = "src/lib.rs"

[[bin]]
name = "repro_build"
path = "src/main.rs"
